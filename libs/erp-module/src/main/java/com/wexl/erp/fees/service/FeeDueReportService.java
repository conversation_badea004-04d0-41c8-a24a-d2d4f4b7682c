package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.repository.FeeHeadRepository;
import com.wexl.erp.fees.repository.FeePaymentDetailsRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.guardian.model.Guardian;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.guardian.service.GuardianService;
import com.wexl.retail.model.Student;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.util.CsvUtils;
import jakarta.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class FeeDueReportService {

  private final FeeHeadRepository feeHeadRepository;
  private final FeePaymentDetailsRepository feePaymentDetailsRepository;
  private final StudentRepository studentRepository;
  private final SectionRepository sectionRepository;
  private final UserService userService;
  private final DateTimeUtil dateTimeUtil;
  private final GuardianService guardianService;

  private static final List<String> CSV_HEADERS =
      Arrays.asList(
          "STUDENT NAME",
          "FATHER NAME",
          "MOTHER NAME",
          "GUARDIAN NAME",
          "MOBILE NUMBER",
          "ADMISSION NUMBER",
          "ROLL NUMBER",
          "SECTION NAME",
          "DATE OF ADMISSION");

  private static final List<String> DUE_CSV_HEADERS = Arrays.asList(
          "STUDENT NAME",
          "ADMISSION NUMBER",
          "ROLL NUMBER",
          "CLASS",
          "STUDENT STATUS",
          "FATHER NAME",
          "FATHER MOBILE NUMBER",
          "MOTHER NAME",
          "MOTHER MOBILE NUMBER",
          "GUARDIAN NAME",
          "GUARDIAN MOBILE NUMBER",
          "TOTAL FEE ASSIGNED",
          "TOTAL FEE PAID",
          "CONCESSION AMOUNT",
          "TOTAL DUE");
  private static final List<String> FEE_HEAD_MASTER_HEADERS = Arrays.asList(
          "Sno", "Admission Number", "Name of Student", "Father Name", "Father Mobile No",
          "Mother Name", "Roll No", "Mother Mobile No", "Class", "Gender", "Student Category",
          "Date of Admission", "Student Status");

  private static final List<String> FEE_HEAD_MASTER_SUB_HEADERS = Arrays.asList(
          "FEE APPLICABLE","GRAND TOTAL", "FEE COLLECTED", "DISCOUNT AMOUNT", "TOTAL DUE AMOUNT");

  private static final List<String> STUDENT_TERM_WISE_HEADERS = Arrays.asList(
          "Student Name", "Student Registration Id", "Class", "Father Name",
          "Class Roll Number", "Student Status", "Fee Head", "Term", "Amount", "Due Date");

  private static final List<String> FEE_HEAD_MASTER_FEE_APPLICABLE_SUB_HEADERS = Arrays.asList(
          "Admission Fee", "Tution Fee", "Late Fee", "Transport Fee", "Tuition Fee - Late Fee",
          "Transport Fee - Late Fee", "Late Fee - Late Fee", "Extended Day Care - Late Fee", "Total");

  private static final List<String> MONTHLY_FEE_SUB_HEADERS = Arrays.asList("APR", "MAY", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC", "JAN", "FEB", "MAR");
  private static final List<String> TERM_WISE_SUB_HEADERS = Arrays.asList("Term 1", "Term 2", "Term 3", "Term 4");

  public void generateFeeDueReportCsv(
      String orgSlug, FeeDto.FeeDueReportRequest request, HttpServletResponse response) {
    List<FeeHead> currentFeeHeads = new ArrayList<>();

    List<FeeDto.FeeDueReportResponse> reportData =
        generateFeeDueReport(orgSlug, request, currentFeeHeads);
    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    generateCsvResponse(reportData, response, reportType, request, currentFeeHeads);
  }

  public List<FeeDto.FeeDueReportResponse> generateFeeDueReport(
      String orgSlug, FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    List<Student> students = getStudentsBySectionUuids(request.sectionUuids());
    List<FeeHead> feeHeads = getFeeHeadsByReportType(orgSlug, students, request);

    currentFeeHeads.addAll(feeHeads);
    Map<Student, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(FeeHead::getStudent));

    String reportType = request.reportType() != null ? request.reportType() : "total_due";
    return students.stream()
        .map(
            student -> {
              List<FeeHead> studentFeeHeads = feeHeadsByStudent.get(student);
              if ("past_due".equals(reportType)) {
                if (studentFeeHeads == null
                    || studentFeeHeads.stream()
                            .map(FeeHead::getBalanceAmount)
                            .filter(Objects::nonNull)
                            .mapToDouble(Double::doubleValue)
                            .sum()
                        <= 0) {
                  return null;
                }
              }
              Double discount = calculateDiscount(student);
              return buildFeeDueReportResponse(discount, student, studentFeeHeads);
            })
        .filter(Objects::nonNull)
        .collect(Collectors.toList());
  }

  private Double calculateDiscount(Student student) {
    var feeHeadList = feeHeadRepository.findAllByStudent(student);
    return (feeHeadList == null || feeHeadList.isEmpty())
        ? 0.0
        : feeHeadList.stream()
            .map(FeeHead::getDiscountAmount)
            .filter(Objects::nonNull)
            .mapToDouble(Double::doubleValue)
            .sum();
  }

  public List<Student> getStudentsBySectionUuids(List<String> sectionUuids) {
    List<Section> sections =
        sectionRepository.findAllByUuidIn(
                sectionUuids.stream().map(UUID::fromString).toList());
    List<Student> students = new ArrayList<>();
    for (Section section : sections) {
      List<Student> sectionStudents = studentRepository.getStudentsBySection(section);
      students.addAll(sectionStudents);
    }
    return students;
  }

  private List<FeeHead> getFeeHeadsByReportType(
      String orgSlug, List<Student> students, FeeDto.FeeDueReportRequest request) {

    List<Long> studentIds = students.stream().map(Student::getId).collect(Collectors.toList());
    List<String> feeNames = request.feeGroupTypes();

    String reportType =
        request.reportType() != null ? request.reportType().toLowerCase() : "total_due";

    return switch (reportType) {
      case "past_due" ->
          feeHeadRepository.findPastDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      case "total_due" ->
          feeHeadRepository.findTotalDueFeeDetails(
              orgSlug,
              studentIds,
              feeNames,
              dateTimeUtil.convertEpochToIso8601(request.fromDate()),
              dateTimeUtil.convertEpochToIso8601(request.toDate()));
      default -> Collections.emptyList();
    };
  }

  private List<FeeHead> getFeeHeadsByStudent(
          String orgSlug, List<Student> students) {
    return feeHeadRepository.findAllByOrgSlugAndStudentIds(orgSlug, students.stream().map(Student::getId).collect(Collectors.toList()));
  }

  private Map<Long, List<PaymentDetail>> getPaymentDetailsByStudents(List<Student> students) {
    Map<Long, List<PaymentDetail>> paymentsByStudent = new HashMap<>();

    for (Student student : students) {
      List<FeeHead> studentFeeHeads = feeHeadRepository.findAllByStudent(student);
      List<PaymentDetail> studentPayments = new ArrayList<>();

      for (FeeHead feeHead : studentFeeHeads) {
        if (feeHead.getPaidAmount() != null && feeHead.getPaidAmount() > 0) {
          var paymentDetails = feePaymentDetailsRepository.findByFeeHead(feeHead);

          for (var paymentDetail : paymentDetails) {
            PaymentDetail payment = new PaymentDetail();
            payment.feeTypeName = feeHead.getFeeType().getName();
            payment.amount = paymentDetail.getAmountPaid();
            payment.date = formatDate(paymentDetail.getCreatedAt());
            payment.receiptNumber = paymentDetail.getFeePayment().getReferenceId() != null ? paymentDetail.getFeePayment().getReferenceId() : "";
            studentPayments.add(payment);
          }
        }
      }

      paymentsByStudent.put(student.getId(), studentPayments);
    }

    return paymentsByStudent;
  }

  private FeeDto.FeeDueReportResponse buildFeeDueReportResponse(
      Double discount, Student student, List<FeeHead> feeHeads) {
    if (feeHeads == null || feeHeads.isEmpty()) {
      return null;
    }

    List<FeeDto.FeeDetailResponse> feeDetails =
        feeHeads.stream().map(this::buildFeeDetailResponse).collect(Collectors.toList());

    Double totalDueAmount =
        feeHeads.stream()
            .mapToDouble(
                feeHead -> feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
            .sum();

    GuardianInfo guardianInfo = extractGuardianInfo(student);

    return FeeDto.FeeDueReportResponse.builder()
        .studentName(userService.getNameByUserInfo(student.getUserInfo()))
        .fatherName(guardianInfo.fatherName)
        .motherName(guardianInfo.motherName)
        .guardianName(guardianInfo.guardianName)
        .mobileNumber(student.getUserInfo().getMobileNumber())
        .admissionNumber(student.getUserInfo().getUserName())
        .rollNumber(student.getClassRollNumber())
        .sectionName(student.getSection() != null ? student.getSection().getName() : "")
        .dateOfAdmission(
            student.getCreatedAt() != null
                ? student
                    .getCreatedAt()
                    .toLocalDateTime()
                    .format(DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                : "")
        .feeDetails(feeDetails)
        .discountAmount(discount)
        .totalDueAmount(totalDueAmount)
        .build();
  }

  private FeeDto.FeeDetailResponse buildFeeDetailResponse(FeeHead feeHead) {
    return FeeDto.FeeDetailResponse.builder()
        .feeTypeName(feeHead.getFeeType().getName())
        .month(
            feeHead.getDueDate() != null
                ? feeHead.getDueDate().format(DateTimeFormatter.ofPattern("MMM")).toUpperCase()
                : "")
        .amount(feeHead.getAmount())
        .paidAmount(feeHead.getPaidAmount() != null ? feeHead.getPaidAmount() : 0.0)
        .balanceAmount(feeHead.getBalanceAmount() != null ? feeHead.getBalanceAmount() : 0.0)
        .dueDate(convertIso8601ToEpoch(feeHead.getDueDate()))
        .status(feeHead.getStatus())
        .build();
  }

  private void generateCsvResponse(
      List<FeeDto.FeeDueReportResponse> reportData,
      HttpServletResponse response,
      String reportType,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {

    String fileName = getFileName(reportType);
    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    List<List<String>> csvData = buildCsvBody(reportData, request, currentFeeHeads);
    if (csvData.isEmpty()) {
      CsvUtils.generateCsv(CSV_HEADERS.toArray(new String[0]), new ArrayList<>(), response);
      return;
    }

    List<String> csvHeaders = csvData.removeFirst();
    CsvUtils.generateCsv(csvHeaders.toArray(new String[0]), csvData, response);
  }

  private String getFileName(String reportType) {
    String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm_dd_MM_yyyy"));
    return switch (reportType) {
      case "past_due" -> "past_due_report_" + timestamp + ".csv";
      case "total_due" -> "total_due_report_" + timestamp + ".csv";
      case "fee_head_master" -> "fee_head_master_report_" + timestamp + ".csv";
      case "student_term_wise" -> "student_term_wise_report_" + timestamp + ".csv";
      default -> "fee_due_report_" + timestamp + ".csv";
    };
  }

  private List<List<String>> buildCsvBody(
      List<FeeDto.FeeDueReportResponse> reportData,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<List<String>> csvBody = new ArrayList<>();

    if (reportData.isEmpty()) {
      return csvBody;
    }

    List<String> headers = generateDynamicHeaders(request, currentFeeHeads);
    csvBody.add(headers);

    for (FeeDto.FeeDueReportResponse report : reportData) {
      List<String> row = buildDynamicDataRow(report, request, currentFeeHeads);
      csvBody.add(row);
    }

    return csvBody;
  }

  private List<String> generateDynamicHeaders(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {

    List<String> headers = new ArrayList<>(CSV_HEADERS);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        headers.add(feeGroupDescription);
      } else {
        for (String feeType : feeTypes) {
          headers.add(feeGroupDescription + " " + feeType);
        }
      }
    }

    headers.add("DISCOUNT AMOUNT");
    headers.add("TOTAL DUE AMOUNT");
    //    headers.addAll(List.of("Delete", "EXTENDED DAY", "Fee Remark"));

    return headers;
  }

  private Map<String, Set<String>> buildFeeGroupStructure(
      FeeDto.FeeDueReportRequest request, List<FeeHead> currentFeeHeads) {
    Map<String, Set<String>> feeGroupStructure = new LinkedHashMap<>();

    if (currentFeeHeads != null) {
      for (FeeHead feeHead : currentFeeHeads) {
        if (feeHead.getFeeMaster() != null
            && feeHead.getFeeMaster().getFeeGroup() != null
            && feeHead.getFeeType() != null) {
          String feeGroupDescription = feeHead.getFeeMaster().getFeeGroup().getName();
          String feeTypeName = feeHead.getFeeType().getName();

          feeGroupStructure
              .computeIfAbsent(feeGroupDescription, k -> new LinkedHashSet<>())
              .add(feeTypeName);
        }
      }
    }

    List<String> requestedFeeGroupTypes = request.feeGroupTypes();
    if (requestedFeeGroupTypes != null && !requestedFeeGroupTypes.isEmpty()) {
      Map<String, Set<String>> filteredStructure = new LinkedHashMap<>();
      for (String requestedType : requestedFeeGroupTypes) {
        if (feeGroupStructure.containsKey(requestedType)) {
          filteredStructure.put(requestedType, feeGroupStructure.get(requestedType));
        }
      }
      return filteredStructure;
    }

    return feeGroupStructure;
  }

  private String getFeeGroupDescriptionForStudent(
      Long studentId, String feeTypeName, List<FeeHead> currentFeeHeads) {
    if (currentFeeHeads == null || studentId == null || feeTypeName == null) {
      return "Unknown Fee Group";
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getId() == (studentId)
          && feeHead.getFeeType() != null
          && feeTypeName.equals(feeHead.getFeeType().getName())
          && feeHead.getFeeMaster() != null
          && feeHead.getFeeMaster().getFeeGroup() != null) {
        return feeHead.getFeeMaster().getFeeGroup().getName();
      }
    }
    return "Unknown Fee Group";
  }

  private List<String> buildDynamicDataRow(
      FeeDto.FeeDueReportResponse report,
      FeeDto.FeeDueReportRequest request,
      List<FeeHead> currentFeeHeads) {
    List<String> row = new ArrayList<>();

    row.add(report.studentName() != null ? report.studentName() : "");
    row.add(report.fatherName() != null ? report.fatherName() : "");
    row.add(report.motherName() != null ? report.motherName() : "");
    row.add(report.guardianName() != null ? report.guardianName() : "");
    row.add(report.mobileNumber() != null ? report.mobileNumber() : "");
    row.add(report.admissionNumber() != null ? report.admissionNumber() : "");
    row.add(report.rollNumber() != null ? report.rollNumber() : "");
    row.add(report.sectionName() != null ? report.sectionName() : "");
    row.add(report.dateOfAdmission() != null ? report.dateOfAdmission() : "");

    Map<String, Map<String, Double>> feeGroupAmounts =
        buildFeeGroupAmountsMap(report, currentFeeHeads);
    Map<String, Set<String>> feeGroupStructure = buildFeeGroupStructure(request, currentFeeHeads);

    for (Map.Entry<String, Set<String>> entry : feeGroupStructure.entrySet()) {
      String feeGroupDescription = entry.getKey();
      Set<String> feeTypes = entry.getValue();

      if (feeTypes.isEmpty()) {
        Double amount =
            feeGroupAmounts.getOrDefault(feeGroupDescription, new HashMap<>()).values().stream()
                .mapToDouble(Double::doubleValue)
                .sum();
        row.add(String.format("%.0f", amount));
      } else {
        for (String feeType : feeTypes) {
          Double amount =
              feeGroupAmounts
                  .getOrDefault(feeGroupDescription, new HashMap<>())
                  .getOrDefault(feeType, 0.0);
          row.add(String.format("%.0f", amount));
        }
      }
    }

    row.add(String.format("%.0f", report.discountAmount()));
    row.add(String.format("%.0f", report.totalDueAmount()));
    //    row.add("0");
    //    row.add("0");
    //    row.add("");

    return row;
  }

  private Map<String, Map<String, Double>> buildFeeGroupAmountsMap(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    Map<String, Map<String, Double>> feeGroupAmounts = new HashMap<>();

    Long studentId = getStudentIdFromReport(report, currentFeeHeads);

    for (FeeDto.FeeDetailResponse detail : report.feeDetails()) {
      String feeGroupDescription =
          getFeeGroupDescriptionForStudent(studentId, detail.feeTypeName(), currentFeeHeads);
      String feeTypeName = detail.feeTypeName();
      Double amount = detail.balanceAmount() != null ? detail.balanceAmount() : 0.0;

      feeGroupAmounts
          .computeIfAbsent(feeGroupDescription, k -> new HashMap<>())
          .merge(feeTypeName, amount, Double::sum);
    }

    return feeGroupAmounts;
  }

  private Long getStudentIdFromReport(
      FeeDto.FeeDueReportResponse report, List<FeeHead> currentFeeHeads) {
    String admissionNumber = report.admissionNumber();
    if (admissionNumber == null) {
      return null;
    }

    for (FeeHead feeHead : currentFeeHeads) {
      if (feeHead.getStudent() != null
          && feeHead.getStudent().getUserInfo() != null
          && admissionNumber.equals(feeHead.getStudent().getUserInfo().getUserName())) {
        return feeHead.getStudent().getId();
      }
    }
    return null;
  }

  private String formatDate(Object dateObj) {
    if (dateObj == null) return "";
    if (dateObj instanceof LocalDateTime) {
      return ((LocalDateTime) dateObj).format(DateTimeFormatter.ofPattern("dd-MM-yyyy"));
    }
    return dateObj.toString();
  }

  public void generateFeeHeadMasterAndStudentTermWiseReportTypeCsv(
      String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {
    if ("fee_head_master".equals(request.reportType())) {
      generateFeeHeadMasterReportCsv(orgSlug, request, response);
    } else if ("student_term_wise".equals(request.reportType())) {
      generateStudentTermWiseReportCsv(orgSlug, request, response);
    }
  }

  public void generateFeeHeadMasterReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    try {
      List<Student> students = getStudentsBySectionUuids(request.sectionUuids());

      List<FeeHead> feeHeads = getFeeHeadsByStudent(orgSlug, students);

      Map<Long, List<PaymentDetail>> paymentsByStudent = getPaymentDetailsByStudents(students);

      Map<Long, StudentFeeData> studentDataMap = processFeeHeadMasterData(students, feeHeads, paymentsByStudent);

      HeaderStructure headerStructure = generateFeeHeadMasterHeaders(feeHeads);

      List<List<String>> csvData = buildFeeHeadMasterCsvData(studentDataMap, headerStructure);

      generateTwoRowHeaderCsvResponse(csvData, headerStructure, getFileName("fee_head_master"), response);

      log.info("Successfully generated Fee Head Master Report with {} students", students.size());

    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Failed to generate Fee Head Master Report", e);
    }
  }

  private void generateStudentTermWiseReportCsv(
          String orgSlug, FeeDto.FeeReportRequest request, HttpServletResponse response) {

    try {
      List<Student> students = getStudentsBySectionUuids(request.sectionUuids());

      List<FeeHead> feeHeads = getFeeHeadsByStudent(orgSlug, students);

      List<StudentTermData> termData = processStudentTermWiseData(students, feeHeads, request);

      List<List<String>> csvData = buildStudentTermWiseCsvData(termData);

      generateSingleHeaderCsvResponse(csvData, STUDENT_TERM_WISE_HEADERS, getFileName("student_term_wise"), response);
    } catch (Exception e) {
      throw new RuntimeException("Failed to generate Student Term-wise Report", e);
    }
  }

  private Map<Long, StudentFeeData> processFeeHeadMasterData(
      List<Student> students, List<FeeHead> feeHeads, Map<Long, List<PaymentDetail>> paymentsByStudent) {

    Map<Long, StudentFeeData> studentDataMap = new HashMap<>();
    Map<Long, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(fh -> fh.getStudent().getId()));

    int sno = 1;
    for (Student student : students) {
      StudentFeeData studentData = extractStudentBasicInfo(student, sno++);

      studentData.feeApplicable = new HashMap<>();
      studentData.feeCollected = new HashMap<>();

      List<FeeHead> studentFeeHeads = feeHeadsByStudent.getOrDefault(student.getId(), new ArrayList<>());
      for (FeeHead feeHead : studentFeeHeads) {
        String feeTypeName = feeHead.getFeeType().getName();
        Double feeAmount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;

        studentData.feeApplicable.merge(feeTypeName, feeAmount, Double::sum);
      }

      List<PaymentDetail> studentPayments = paymentsByStudent.getOrDefault(student.getId(), new ArrayList<>());
      for (PaymentDetail payment : studentPayments) {
        if (!studentData.feeCollected.containsKey(payment.feeTypeName)) {
          studentData.feeCollected.put(payment.feeTypeName, new ArrayList<>());
        }
        studentData.feeCollected.get(payment.feeTypeName).add(payment);
      }

      studentData.grandTotal = studentData.feeApplicable.values().stream()
          .mapToDouble(Double::doubleValue).sum();
      studentData.totalCollected = studentPayments.stream()
          .mapToDouble(payment -> payment.amount)
          .sum();
      studentData.discounts = calculateDiscount(student);
      studentData.dueAmount = studentData.grandTotal - studentData.totalCollected - studentData.discounts;

      studentDataMap.put(student.getId(), studentData);
    }

    return studentDataMap;
  }

  private StudentFeeData extractStudentBasicInfo(Student student, int sno) {
    StudentFeeData studentData = new StudentFeeData();
    studentData.sno = sno;
    studentData.admissionNumber = student.getUserInfo().getUserName();
    studentData.studentName = userService.getNameByUserInfo(student.getUserInfo());

    var guardianInfo = extractGuardianInfo(student);
    studentData.fatherName = guardianInfo.fatherName;
    studentData.fatherMobile = guardianInfo.fatherMobile;
    studentData.motherName = guardianInfo.motherName;
    studentData.motherMobile = guardianInfo.motherMobile;

    studentData.rollNumber = student.getRollNumber();
    studentData.classRollNumber = student.getClassRollNumber();
    studentData.className = student.getSection() != null ? student.getSection().getName() : "";
    studentData.gender = student.getUserInfo().getGender() != null ? student.getUserInfo().getGender().toString() : "";
    studentData.studentCategory = "DAY SCHOLAR";
    studentData.dateOfAdmission = formatDate(student.getCreatedAt());
    studentData.studentStatus = student.getActive() == '1' ? "ACTIVE" : "INACTIVE";

    return studentData;
  }

  private GuardianInfo extractGuardianInfo(Student student) {
    try {
      var father = student.getGuardians().stream()
              .filter(x -> x.getRelationType().equals(GuardianRole.FATHER));
      var mother = student.getGuardians().stream()
              .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER));
      var guardian = student.getGuardians().stream()
              .filter(x -> x.getRelationType().equals(GuardianRole.GUARDIAN));

      GuardianInfo info = new GuardianInfo();
      info.fatherName = father.findAny().map(guardianService::getGuardianName).orElse(null);
      info.fatherMobile = father.findAny().map(Guardian::getMobileNumber).orElse(null);
      info.motherName = mother.findAny().map(guardianService::getGuardianName).orElse(null);
      info.motherMobile = mother.findAny().map(Guardian::getMobileNumber).orElse(null);
      info.guardianName = guardian.findAny().map(guardianService::getGuardianName).orElse(null);
      info.guardianMobile = guardian.findAny().map(Guardian::getMobileNumber).orElse(null);

      return info;
    } catch (Exception e) {
      log.warn("Error extracting guardian info for student {}: {}", student.getId(), e.getMessage());
      GuardianInfo info = new GuardianInfo();
      info.fatherName = "";
      info.fatherMobile = "";
      info.motherName = "";
      info.motherMobile = "";
      return info;
    }
  }

  private HeaderStructure generateFeeHeadMasterHeaders(List<FeeHead> feeHeads) {
    Set<String> feeTypes = feeHeads.stream()
        .map(fh -> fh.getFeeType().getName())
        .collect(Collectors.toCollection(LinkedHashSet::new));

    List<String> feeGroupDescriptions = feeHeads.stream()
        .map(fh -> fh.getFeeMaster().getFeeGroup().getName())
        .distinct()
        .collect(Collectors.toList());

    HeaderStructure structure = new HeaderStructure();

    List<String> mainHeaders = new ArrayList<>(FEE_HEAD_MASTER_HEADERS);

    mainHeaders.add("FEE APPLICABLE");
    structure.subHeaders=FEE_HEAD_MASTER_FEE_APPLICABLE_SUB_HEADERS;

    mainHeaders.add("Total");
    mainHeaders.add("Discounts");
    mainHeaders.add("Grand Total");

    mainHeaders.add("FEE COLLECTED");
    mainHeaders.addAll(feeTypes);
    mainHeaders.add("Total");
    mainHeaders.add("Due");

    List<String> subHeaders = new ArrayList<>();

    subHeaders.addAll(feeTypes);
    subHeaders.add("Total");
    subHeaders.add("Discounts");
    subHeaders.add("Grand Total");
      subHeaders.add("Date");
      subHeaders.add("Receipt No");
    subHeaders.add("Total");
    subHeaders.add("Due");

    structure.mainHeaders = mainHeaders;
    structure.subHeaders = subHeaders;
    structure.feeTypes = new ArrayList<>(feeTypes);
    structure.feeGroupDescriptions = feeGroupDescriptions;

    return structure;
  }

  private static class StudentFeeData {
    int sno;
    String admissionNumber;
    String studentName;
    String fatherName;
    String fatherMobile;
    String motherName;
    String motherMobile;
    String rollNumber;
    String classRollNumber;
    String className;
    String gender;
    String studentCategory;
    String dateOfAdmission;
    String studentStatus;
    Map<String, Double> feeApplicable;
    Map<String, List<PaymentDetail>> feeCollected;
    Double grandTotal;
    Double totalCollected;
    Double dueAmount;
    Double discounts;
  }

  private static class PaymentDetail {
    String feeTypeName;
    String date;
    String receiptNumber;
    Double amount;
  }

  private static class GuardianInfo {
    String fatherName;
    String fatherMobile;
    String motherName;
    String motherMobile;
    String guardianName;
    String guardianMobile;
  }

  private static class HeaderStructure {
    List<String> mainHeaders;
    List<String> subHeaders;
    List<String> feeTypes;
    List<String> feeGroupDescriptions;
  }

  private static class StudentTermData {
    String studentName;
    String registrationId;
    String className;
    String fatherName;
    String classRollNumber;
    String studentStatus;
    String feeHead;
    String term;
    Double amount;
    String dueDate;
  }

  private List<StudentTermData> processStudentTermWiseData(
          List<Student> students, List<FeeHead> feeHeads, FeeDto.FeeReportRequest request) {

    List<StudentTermData> termDataList = new ArrayList<>();
    Map<Long, List<FeeHead>> feeHeadsByStudent =
        feeHeads.stream().collect(Collectors.groupingBy(fh -> fh.getStudent().getId()));

    for (Student student : students) {
      List<FeeHead> studentFeeHeads = feeHeadsByStudent.getOrDefault(student.getId(), new ArrayList<>());

      var guardianInfo = extractGuardianInfo(student);

      for (FeeHead feeHead : studentFeeHeads) {
        if (request.reportType() != null && request.reportType().contains("term")) {
          StudentTermData termData = new StudentTermData();
          termData.studentName = userService.getNameByUserInfo(student.getUserInfo());
          termData.registrationId = student.getUserInfo().getUserName();
          termData.className = student.getSection() != null ? student.getSection().getName() : "";
          termData.fatherName = guardianInfo.fatherName;
          termData.classRollNumber = student.getClassRollNumber();
          termData.studentStatus = student.getActive() == '1' ? "active" : "inactive";
          termData.feeHead = feeHead.getFeeType().getName();
          termData.term = determineTerm(feeHead, request);
          termData.amount = feeHead.getAmount() != null ? feeHead.getAmount() : 0.0;
          termData.dueDate = formatDate(feeHead.getDueDate());

          termDataList.add(termData);
        }
      }
    }

    return termDataList;
  }

  private String determineTerm(FeeHead feeHead, FeeDto.FeeReportRequest request) {
    return "Term 1";
  }

  private List<List<String>> buildFeeHeadMasterCsvData(
      Map<Long, StudentFeeData> studentDataMap, HeaderStructure headerStructure) {

    List<List<String>> csvData = new ArrayList<>();

    for (StudentFeeData studentData : studentDataMap.values()) {
      List<String> row = new ArrayList<>();

      row.add(String.valueOf(studentData.sno));
      row.add(safeString(studentData.admissionNumber));
      row.add(safeString(studentData.studentName));
      row.add(safeString(studentData.fatherName));
      row.add(safeString(studentData.fatherMobile));
      row.add(safeString(studentData.motherName));
      row.add(safeString(studentData.rollNumber));
      row.add(safeString(studentData.motherMobile));
      row.add(safeString(studentData.className));
      row.add(safeString(studentData.gender));
      row.add(safeString(studentData.studentCategory));
      row.add(safeString(studentData.dateOfAdmission));
      row.add(safeString(studentData.studentStatus));

      for (String feeType : headerStructure.feeTypes) {
        Double amount = studentData.feeApplicable.getOrDefault(feeType, 0.0);
        row.add(formatAmount(amount));
      }

      row.add(formatAmount(studentData.grandTotal));
      row.add(formatAmount(studentData.discounts));
      row.add(formatAmount(studentData.grandTotal - studentData.discounts));

      for (String feeType : headerStructure.feeTypes) {
        List<PaymentDetail> payments = studentData.feeCollected.getOrDefault(feeType, new ArrayList<>());
        if (!payments.isEmpty()) {
          PaymentDetail latestPayment = payments.get(payments.size() - 1);
          row.add(safeString(latestPayment.date));
          row.add(safeString(latestPayment.receiptNumber));
          row.add(formatAmount(latestPayment.amount));
        } else {
          row.add("");
          row.add("");
          row.add("0");
        }
      }

      row.add(formatAmount(studentData.totalCollected));
      row.add(formatAmount(studentData.dueAmount));

      csvData.add(row);
    }

    return csvData;
  }

  private String safeString(String value) {
    return value != null ? value : "";
  }

  private String formatAmount(Double amount) {
    return amount != null ? String.format("%.2f", amount) : "0.00";
  }

  private List<List<String>> buildStudentTermWiseCsvData(List<StudentTermData> termDataList) {
    return termDataList.stream()
        .map(termData -> Arrays.asList(
            safeString(termData.studentName),
            safeString(termData.registrationId),
            safeString(termData.className),
            safeString(termData.fatherName),
            safeString(termData.classRollNumber),
            safeString(termData.studentStatus),
            safeString(termData.feeHead),
            safeString(termData.term),
            formatAmount(termData.amount),
            safeString(termData.dueDate)
        ))
        .collect(Collectors.toList());
  }

  private void generateTwoRowHeaderCsvResponse(
      List<List<String>> csvData, HeaderStructure headerStructure, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    try {
      var writer = response.getWriter();

      String mainHeaderLine = headerStructure.mainHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.joining(","));
      writer.println(mainHeaderLine);

      String subHeaderLine = headerStructure.subHeaders.stream()
          .map(this::escapeCsvValue)
          .collect(Collectors.joining(","));
      writer.println(subHeaderLine);

      for (List<String> row : csvData) {
        String dataLine = row.stream()
            .map(this::escapeCsvValue)
            .collect(Collectors.joining(","));
        writer.println(dataLine);
      }

      writer.flush();
    } catch (Exception e) {
      throw new RuntimeException("Error generating CSV response", e);
    }
  }

  private void generateSingleHeaderCsvResponse(
      List<List<String>> csvData, List<String> headers, String fileName, HttpServletResponse response) {

    response.setContentType("text/csv; charset=UTF-8");
    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");

    try {
      if (csvData.isEmpty()) {
        CsvUtils.generateCsv(headers.toArray(new String[0]), new ArrayList<>(), response);
        return;
      }

      CsvUtils.generateCsv(headers.toArray(new String[0]), csvData, response);
    } catch (Exception e) {
      log.error("Error generating single header CSV response for file: {}", fileName, e);
      throw new RuntimeException("Error generating CSV response", e);
    }
  }

  private String escapeCsvValue(String value) {
    if (value == null) return "";
    if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
      return "\"" + value.replace("\"", "\"\"") + "\"";
    }
    return value;
  }
}
